@echo off
REM ComfyUI Windows 启动脚本
REM 作者: 自动生成
REM 日期: 2025年6月22日

echo === ComfyUI Windows 启动脚本 ===
echo.

REM 检查WSL是否安装
wsl --status >nul 2>&1
if %ERRORLEVEL% neq 0 (
    echo 错误: WSL未安装或未启动
    echo 请确保WSL已正确安装并启动
    pause
    exit /b 1
)

echo 检查WSL状态...
wsl --list --verbose

echo.
echo 启动ComfyUI (在WSL中)...
echo 请等待ComfyUI启动完成...
echo.

REM 使用wsl命令执行bash脚本
wsl bash /mnt/e/wsl/ComfyUI/start_comfyui.sh

echo.
echo ComfyUI已停止运行
pause
