# ComfyUI-Manager: Documentation

This directory contains documentation for the ComfyUI-Manager, providing guides and tutorials for users in multiple languages.

## Directory Structure

The documentation is organized into language-specific directories:

- **en/**: English documentation
- **ko/**: Korean documentation

## Core Documentation Files

### Command-Line Interface

- **cm-cli.md**: Documentation for the ComfyUI-Manager Command Line Interface (CLI), which allows using manager functionality without the UI.

### Advanced Features

- **use_aria2.md**: Guide for using the aria2 download accelerator with ComfyUI-Manager for faster model downloads.

## Documentation Standards

The documentation follows these standards:

1. **Markdown Format**: All documentation is written in Markdown for easy rendering on GitHub and other platforms
2. **Language-specific Directories**: Content is separated by language to facilitate localization
3. **Feature-focused Documentation**: Each major feature has its own documentation file
4. **Updated with Releases**: Documentation is kept in sync with software releases

## Contributing to Documentation

When contributing new documentation:

1. Place files in the appropriate language directory
2. Use clear, concise language appropriate for the target audience
3. Include examples where helpful
4. Consider adding screenshots or diagrams for complex features
5. Maintain consistent formatting with existing documentation

This documentation directory will continue to grow to support the expanding feature set of ComfyUI-Manager.