# ComfyUI PowerShell 启动脚本
# 作者: 自动生成
# 日期: 2025年6月22日

Write-Host "=== ComfyUI PowerShell 启动脚本 ===" -ForegroundColor Green
Write-Host ""

# 检查WSL是否可用
try {
    $wslStatus = wsl --status 2>&1
    Write-Host "WSL状态检查通过" -ForegroundColor Green
} catch {
    Write-Host "错误: WSL未安装或未启动" -ForegroundColor Red
    Write-Host "请确保WSL已正确安装并启动" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 显示WSL分发版本
Write-Host "当前WSL分发版本:" -ForegroundColor Cyan
wsl --list --verbose

Write-Host ""
Write-Host "启动ComfyUI (在WSL中)..." -ForegroundColor Yellow
Write-Host "请等待ComfyUI启动完成..." -ForegroundColor Yellow
Write-Host ""

# 获取WSL IP地址
$wslIP = wsl hostname -I
$wslIP = $wslIP.Trim().Split()[0]

Write-Host "WSL IP地址: $wslIP" -ForegroundColor Cyan
Write-Host "ComfyUI将在启动后可通过以下地址访问:" -ForegroundColor Green
Write-Host "http://$wslIP:8188" -ForegroundColor White
Write-Host ""

# 使用wsl命令执行bash脚本
try {
    wsl bash /mnt/e/wsl/ComfyUI/start_comfyui.sh
} catch {
    Write-Host "启动过程中出现错误" -ForegroundColor Red
} finally {
    Write-Host ""
    Write-Host "ComfyUI已停止运行" -ForegroundColor Yellow
    Read-Host "按回车键退出"
}
