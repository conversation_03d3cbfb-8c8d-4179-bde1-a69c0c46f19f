#!/bin/bash

# ComfyUI 启动脚本 (WSL版本)
# 作者: 自动生成
# 日期: 2025年6月22日
# 使用方法: 在Windows PowerShell中运行: wsl bash /mnt/e/wsl/ComfyUI/start_comfyui.sh

echo "=== ComfyUI WSL 启动脚本 ==="

# 切换到ComfyUI目录 (WSL路径)
COMFYUI_DIR="/mnt/e/wsl/ComfyUI"
echo "切换到ComfyUI目录: $COMFYUI_DIR"
cd "$COMFYUI_DIR"

# 检查目录是否存在
if [ ! -d "$COMFYUI_DIR" ]; then
    echo "错误: ComfyUI目录不存在: $COMFYUI_DIR"
    exit 1
fi

# 检查main.py是否存在
if [ ! -f "main.py" ]; then
    echo "错误: main.py文件不存在，请确保在正确的ComfyUI目录中"
    exit 1
fi

# 检查conda是否已安装
if ! command -v conda &> /dev/null; then
    echo "错误: conda 未找到，请确保已安装conda"
    exit 1
fi

# 初始化conda（如果需要）
echo "初始化conda环境..."
eval "$(conda shell.bash hook)"

# 激活conda虚拟环境
echo "激活conda虚拟环境: comfyui"
conda activate comfyui

# 检查虚拟环境是否激活成功
if [[ "$CONDA_DEFAULT_ENV" != "comfyui" ]]; then
    echo "错误: 无法激活comfyui虚拟环境"
    echo "请确保虚拟环境存在: conda env list"
    exit 1
fi

echo "成功激活虚拟环境: $CONDA_DEFAULT_ENV"

# 检查Python和主要依赖
echo "检查Python版本..."
python --version

echo "检查torch是否安装..."
python -c "import torch; print(f'PyTorch版本: {torch.__version__}')" 2>/dev/null || echo "警告: PyTorch未安装或有问题"

# 显示网络信息
echo "=== 网络信息 ==="
echo "WSL IP地址:"
hostname -I

# 启动ComfyUI
echo "=== 启动ComfyUI ==="
echo "ComfyUI将在以下地址启动:"
echo "- WSL内部访问: http://localhost:8188"
WSL_IP=$(hostname -I | awk '{print $1}')
echo "- Windows访问: http://$WSL_IP:8188"
echo ""
echo "在Windows浏览器中访问: http://$WSL_IP:8188"
echo "按 Ctrl+C 停止服务器"
echo "=========================="

# 启动ComfyUI，监听所有网络接口
python main.py --listen 0.0.0.0 --port 8188 --auto-launch
