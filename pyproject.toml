[project]
name = "ComfyUI"
version = "0.3.43"
readme = "README.md"
license = { file = "LICENSE" }
requires-python = ">=3.9"
dependencies = [
    "aiohttp>=3.11.8",
    "alembic>=1.16.2",
    "av>=14.2.0",
    "comfyui-embedded-docs==0.2.3",
    "comfyui-frontend-package==1.23.4",
    "comfyui-workflow-templates==0.1.30",
    "einops>=0.8.1",
    "kornia>=0.7.1",
    "numpy>=1.25.0",
    "pillow>=11.2.1",
    "pip>=25.1.1",
    "psutil>=7.0.0",
    "pydantic~=2.0",
    "pydantic-settings~=2.0",
    "pyyaml>=6.0.2",
    "safetensors>=0.4.2",
    "scipy>=1.13.1",
    "sentencepiece>=0.2.0",
    "soundfile>=0.13.1",
    "spandrel>=0.4.1",
    "sqlalchemy>=2.0.41",
    "tokenizers>=0.13.3",
    "torch>=2.7.1",
    "torchaudio>=2.7.1",
    "torchsde>=0.2.6",
    "torchvision>=0.22.1",
    "tqdm>=4.67.1",
    "transformers>=4.37.2",
    "yarl>=1.18.0",
]

[project.urls]
homepage = "https://www.comfy.org/"
repository = "https://github.com/comfyanonymous/ComfyUI"
documentation = "https://docs.comfy.org/"

[tool.ruff]
lint.select = [
  "N805", # invalid-first-argument-name-for-method
  "S307", # suspicious-eval-usage
  "S102", # exec
  "T",    # print-usage
  "W",
  # The "F" series in Ruff stands for "Pyflakes" rules, which catch various Python syntax errors and undefined names.
  # See all rules here: https://docs.astral.sh/ruff/rules/#pyflakes-f
  "F",
]
exclude = ["*.ipynb"]
